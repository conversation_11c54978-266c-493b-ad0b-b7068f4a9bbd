import React, { useEffect, useState } from 'react';
import { CustomCollapse, CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, ConfigProvider } from 'antd';
import { Checkbox } from 'antd';
import _ from 'lodash';
import { useCadParseMutation, useGetAutoProgramConfigQuery, useRunFullAutoProgramMutation, useRunSemiAutoProgramMutation } from '../services/product';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled, setIsTrainingRunning, setCurTrainingTaskStartTime, setShouldRunReevaluateAfterRetrain, setIsAutoProgramTriggeredFromTemplateEditor, setIsAgentParamRegenRunning } from '../reducer/setting';
import { useModelUpdateTriggerMutation } from '../services/inference';
import { modelTypes } from '../common/const';
import { useNavigate } from 'react-router-dom';


const ConfigAutoProgram = (props) => {
  const {
    isOpened,
    setIsOpened,
    productId,
    roi,
    step,
    onFinish,
    isFullAutoProgram,
    cadInfo,
    isNotInitialSemiAutoProgram,
  } = props;

  const navigate = useNavigate();

  const dispatch = useDispatch();
  const [retrainTrigger] = useModelUpdateTriggerMutation();

  const { t } = useTranslation();

  const [selectedInspectionTypes, setSelectedInspectionTypes] = useState({}); // a map { "mount": [...], "polarity": [...] }

  const { data: autoProgrammingConfigTemplate, refetch: refetchAutoProgrammingConfigTemplate } = useGetAutoProgramConfigQuery();
  const [runFullAutoProgram] = useRunFullAutoProgramMutation();
  const [runSemiAutoProgram] = useRunSemiAutoProgramMutation();
  const [parseCAD] = useCadParseMutation();

  const handleAutoGenerateAgentParams = async (productId) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoGeneratingAgentParams')));

    const res = await retrainTrigger({
      model_types: [
        modelTypes.mounting3DModel,
        modelTypes.lead3DModel,
        modelTypes.solder3DModel,
        modelTypes.lead2DV2Model,
        modelTypes.solder2DModel,
        // modelTypes.leadModel,
        // modelTypes.mountingModel,
      ],
      golden_product_id: Number(productId),
      // update_parameters: true,
    });

    if (res.error) {
      aoiAlert(t('notification.error.autoGenerateAgentParams'), ALERT_TYPES.COMMON_ERROR);
      console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return false;
    }

    dispatch(setIsAgentParamRegenRunning(true));
    dispatch(setIsTrainingRunning(true));
    dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
    return true;
  };

  useEffect(() => {
    if (!autoProgrammingConfigTemplate) return;

    const cloned = _.cloneDeep(autoProgrammingConfigTemplate.component_for_line_item);
    // delete cloned['TEXT'];
    cloned['TEXT'] = [];
    setSelectedInspectionTypes(cloned);
  }, [autoProgrammingConfigTemplate]);

  useEffect(() => {
    if (!isOpened) return;
    refetchAutoProgrammingConfigTemplate();
  }, [isOpened]);

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('productDefine.configAutoProgram')}
      </span>}
      footer={null}
      width={386}
    >
      <div className='flex p-4 flex-col gap-2 self-stretch'>
        <div className='flex flex-col items-start gap-1 self-stretch px-0.5 py-2 rounded-sm'>
          <span className='font-source text-base font-normal leading-[150%] tracking-[0.48px]'>
            {t('autoProgramming.allModelledInspectionTypes')}
          </span>
          <span className='text-gray-4 font-source text-sm font-normal leading-[150%]'>
            {t('autoProgramming.wahtAreTheTypes')}
          </span>
        </div>
        <ConfigProvider
          theme={{
            components: {
              Collapse: {
                headerPadding: '0px 0 0px 8px',
                contentPadding: '0 0 0 8px',
              }
            }
          }}
        >
          <CustomCollapse
            style={{ width: '100%' }}
            items={_.map(_.keys(autoProgrammingConfigTemplate?.component_for_line_item), k => {
              const componentTypes = _.get(autoProgrammingConfigTemplate, `component_for_line_item.${k}`, []);
              const selectedComponents = _.get(selectedInspectionTypes, k, []);
              const allSelected = componentTypes.length > 0 && componentTypes.length === selectedComponents.length;

              return {
                key: k,
                label: <div className={`flex h-[32px] px-2 items-center gap-2 justify-between`}>
                  <div className='flex items-center gap-2'>
                    <Checkbox
                      size='small'
                      checked={allSelected}
                      onClick={(e) => {
                        e.stopPropagation();
                        const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                        if (allSelected) {
                          // Uncheck all - clear the array
                          newInspTypes[k] = [];
                        } else {
                          // Check all - add all component types
                          newInspTypes[k] = [...componentTypes];
                        }
                        setSelectedInspectionTypes(newInspTypes);
                      }}
                    />
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {/* {k} */}
                      {t(`autoProgramming.inspectionTypes.${k}`, k)}
                    </span>
                  </div>
                </div>,
                children: <div className='flex flex-col pl-4'>
                  {_.map(componentTypes, (v) => (
                    <div className='flex items-center gap-2' key={v}>
                      <Checkbox
                        size='small'
                        checked={_.includes(selectedComponents, v)}
                        onChange={(e) => {
                          const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                          if (e.target.checked) {
                            newInspTypes[k] = [...(newInspTypes[k] || []), v];
                          } else {
                            newInspTypes[k] = _.filter(newInspTypes[k], (t) => t !== v);
                          }
                          setSelectedInspectionTypes(newInspTypes);
                        }}
                      />
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {/* {v} */}
                        {t(`autoProgramming.componentTypes.${v}`, v)}
                      </span>
                    </div>
                  ))}
                </div>,
              };
            })}
          />
        </ConfigProvider>
        <div className='flex items-center gap-2 self-stretch'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            style={{ width: '50%' }}
            type='primary'
            onClick={() => {
              const run = async (productId, selectedInspectionTypes, step, roi, cadInfo, isFullAutoProgram, isNotInitialSemiAutoProgram) => {
                dispatch(setIsContainerLvlLoadingEnabled(true));
                dispatch(setContainerLvlLoadingMsg(t('loader.autoProgramming')));

                const payload = {
                  product_id: Number(productId),
                  config: {
                    component_for_line_item: selectedInspectionTypes,
                  },
                  step,
                };

                if (isFullAutoProgram) {
                  payload.roi = {
                    type: 'obb',
                    points: [
                      {
                        x: _.round(_.get(roi, 'pMin.x', 0), 0),
                        y: _.round(_.get(roi, 'pMin.y', 0), 0),
                      },
                      {
                        x: _.round(_.get(roi, 'pMax.x', 0), 0) - 1,
                        y: _.round(_.get(roi, 'pMax.y', 0), 0) - 1,
                      }
                    ],
                    center: null,
                    angle: 0,
                  };
                } else {
                  payload.roi = {
                    type: 'obb',
                    points: [
                      {
                        x: _.round(_.get(roi, 'pMin.x', 0), 0),
                        y: _.round(_.get(roi, 'pMin.y', 0), 0),
                      },
                      {
                        x: _.round(_.get(roi, 'pMax.x', 0), 0) - 1,
                        y: _.round(_.get(roi, 'pMax.y', 0), 0) - 1,
                      }
                    ],
                    center: null,
                    angle: 0,
                  };

                  // console.log('parseRules when submit', parseRules);
                  // if (_.isEmpty(parseRules) || _.isEmpty(currentFileUri)) return;
                  if (_.isEmpty(cadInfo.rules.delimiter)) {
                    aoiAlert(t('notification.error.selectADelimeter'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  if (!_.isNumber(cadInfo.rules.unit_multiplier)) {
                    aoiAlert(t('notification.error.selectAUnitMultiplier'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  if (
                    // !_.isInteger(parseRules.partNumberCol) ||
                    !_.isInteger(cadInfo.rules.package_number_col) || !_.isInteger(cadInfo.rules.x_col) || !_.isInteger(cadInfo.rules.y_col)) {
                    aoiAlert(t('notification.error.selectAllColumns'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  if (_.get(cadInfo.rules, 'ignore_bot_layer', false) && !_.isString(cadInfo.rules, 'bot_layer_identifier', null) && !_.isInteger(cadInfo.rules.layer_col)) {
                    aoiAlert(t('notification.error.selectBotLayerIdentifier'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  if (!_.isInteger(cadInfo.rules.data_row_begin) || !_.isInteger(cadInfo.rules.data_row_end)) {
                    aoiAlert(t('notification.error.enterFirstAndLastRow'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  if (!_.isInteger(cadInfo.rules.designator_col)) {
                    aoiAlert(t('notification.error.selectRefDesignatorColumn'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }

                  payload.cad_info = cadInfo;
                }

                let res;

                if (isFullAutoProgram) {
                  res = await runFullAutoProgram(payload);
                } else {
                  res = await runSemiAutoProgram(payload);
                }

                if (res.error) {
                  if (isNotInitialSemiAutoProgram) {
                    aoiAlert(t('autoProgramming.semiAutoProgramFail'), ALERT_TYPES.COMMON_ERROR);
                    console.error('runSemiAutoProgram error:', _.get(res, 'error.message', ''));
                    dispatch(setIsContainerLvlLoadingEnabled(false));
                    dispatch(setContainerLvlLoadingMsg(''));
                    return;
                  }

                  if (!isFullAutoProgram) {
                    aoiAlert(t('autoProgramming.semiAutoProgramFail'), ALERT_TYPES.COMMON_ERROR);
                    console.error('runSemiAutoProgram error:', _.get(res, 'error.message', ''));

                    // run parse CAD to get the coordinates
                    const res1 = await parseCAD(cadInfo);

                    if (res1.error) {
                      aoiAlert(t('notification.error.parseCAD'), ALERT_TYPES.COMMON_ERROR);
                      console.error('parseCAD error:', _.get(res1, 'error.message', ''));
                      dispatch(setIsContainerLvlLoadingEnabled(false));
                      dispatch(setContainerLvlLoadingMsg(''));
                      return;
                    }

                    await onFinish(_.get(res1, 'data', []));
                    setIsOpened(false);
                    dispatch(setIsContainerLvlLoadingEnabled(false));
                    dispatch(setContainerLvlLoadingMsg(''));
                    return;
                  }

                  console.error('runFullAutoProgramming error:', _.get(res, 'error.message', ''));
                  dispatch(setIsContainerLvlLoadingEnabled(false));
                  dispatch(setContainerLvlLoadingMsg(''));
                  return;
                }

                if (!isFullAutoProgram) {
                  // we expect features will be generated in backend hence we redirect users to template editor
                  dispatch(setShouldRunReevaluateAfterRetrain({ productId: Number(productId), shouldRun: true}));

                  await handleAutoGenerateAgentParams(productId);
                  navigate(`/teach?product-id=${productId}&from-auto-programming=true`);
                  return;
                }

                dispatch(setIsAutoProgramTriggeredFromTemplateEditor(true));
                dispatch(setShouldRunReevaluateAfterRetrain({ productId: Number(productId), shouldRun: true}));
                await handleAutoGenerateAgentParams(productId);

                // await onFinish();

                setIsOpened(false);

                // dispatch(setIsContainerLvlLoadingEnabled(false));
                // dispatch(setContainerLvlLoadingMsg(''));
              };

              run(productId, selectedInspectionTypes, step, roi, cadInfo, isFullAutoProgram, isNotInitialSemiAutoProgram);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.startAutoProgramming')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  );
};

export default ConfigAutoProgram;