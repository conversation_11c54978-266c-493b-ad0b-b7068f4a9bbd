import { Button, ConfigProvider, Input, Table, Tabs, Tooltip, Select } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { CopyOutlined } from '@ant-design/icons';
import {
  filletLowerThreshold,
  filletOpenThreshold,
  filletUpperThreshold,
  filletVolumeRatio,
  solderInspection3D,
  leadInspection2D,
  solderInspection2D,
  solderValidRatioList,
  padValidRatioList,
  tipValidRatioList,
  validRatioList,
} from '../common/const';
import { CustomCollapse } from '../common/styledComponent';
import ParamDisplay from './ParamDisplay';
import CommonTable from './CommonTable';
import { blurOcr } from '../common/util';


const InferenceResult = (props) => {
  const {
    featureError,
    maskList,
    onMaskChange,
    maskInfo,
    isTraingSet,
  } = props;
  console.log('featureError', featureError);

  return (
    <div className='flex flex-1 self-stretch w-full flex-shrink-0'>
      <div className='flex flex-col p-1 gap-2 self-stretch w-full'>
        {_.map(_.keys(featureError.parsedError), (errorType, idx) => {
          if (_.includes(['string'], _.get(featureError, `parsedError.${errorType}.reference.type`, ''))) return <ParamString
            parsedError={_.get(featureError, `parsedError.${errorType}`)}
            errorType={errorType}
          />;
          // done
          if (_.includes(['float', 'int'], _.get(featureError, `parsedError.${errorType}.reference.type`, ''))) return <ParamNumber
            parsedError={_.get(featureError, `parsedError.${errorType}`)}
            errorType={errorType}
          />;
          if (_.includes(['bool'], _.get(featureError, `parsedError.${errorType}.reference.type`, ''))) return <ParamBool
            parsedError={_.get(featureError, `parsedError.${errorType}`)}
            errorType={errorType}
          />;
          // done
          if (_.includes(['minMaxRange'], _.get(featureError, `parsedError.${errorType}.reference.type`, ''))) return <ParamMinMaxRange
            parsedError={_.get(featureError, `parsedError.${errorType}`)}
            errorType={errorType}
          />;
          // done
          if (filletVolumeRatio === errorType) return <FilletVolumeRatio
            parsedError={_.get(featureError, `parsedError.${errorType}`)}
            filletOpenThreshold={_.get(featureError, `parsedError.${errorType}.reference.filletOpenThreshold`, 0)}
            filletUpperThreshold={_.get(featureError, `parsedError.${errorType}.reference.filletUpperThreshold`, 0)}
            filletLowerThreshold={_.get(featureError, `parsedError.${errorType}.reference.filletLowerThreshold`, 0)}

          />;
          if (
            (
              _.get(featureError, `parsedError.${errorType}.detail`) === leadInspection2D &&
              [solderValidRatioList, padValidRatioList, tipValidRatioList].includes(errorType)
            ) ||
            (
              _.get(featureError, `parsedError.${errorType}.detail`) === solderInspection2D &&
              errorType === validRatioList
            )
          ) return <></>;
          // if (
          //   _.get(featureError, `parsedError.${errorType}.detail`) === leadInspection2D &&
          //   [solderValidRatioList, padValidRatioList, tipValidRatioList].includes(errorType)
          // ) return <ParamFloatList
          //   parsedError={_.get(featureError, `parsedError.${errorType}`)}
          //   errorType={errorType}
          // />;
          // if (
          //   _.get(featureError, `parsedError.${errorType}.detail`) === solderInspection2D &&
          //   errorType === validRatioList
          // ) return <ParamFloatList
          //   parsedError={_.get(featureError, `parsedError.${errorType}`)}
          //   errorType={errorType}
          // />;
        })}
        {_.find(_.keys(featureError.parsedError), paramName => _.get(featureError, `parsedError.${paramName}.detail`) === leadInspection2D) && <Lead2DRatioList
          featureError={featureError}
          maskList={maskList}
          onMaskChange={onMaskChange}
          maskInfo={maskInfo}
        />}
        {_.find(_.keys(featureError.parsedError), paramName => _.get(featureError, `parsedError.${paramName}.detail`) === solderInspection2D) && <Solder2DRatioList
          featureError={featureError}
          maskList={maskList}
          onMaskChange={onMaskChange}
          maskInfo={maskInfo}
          isTraingSet={isTraingSet}
        />}
      </div>
    </div>
  );
};

const Solder2DRatioList = (props) => {
  const {
    featureError,
    maskList,
    onMaskChange,
    maskInfo,
    isTraingSet,
  } = props;
  // console.log('featureError', featureError);

  const { t } = useTranslation();

  const triggerFromSelect = useRef(false);

  const masks = Array.isArray(maskList) ? maskList : (maskList ? [maskList] : []);
  const [showMask, setShowMask] = useState(false);
  const [selectedIdx, setSelectedIdx] = useState(0);

  // console.log('featureError', featureError);

  useEffect(() => {
    if (isTraingSet) return;

    if (triggerFromSelect.current) {
      triggerFromSelect.current = false;
      return;
    }
    if (maskInfo.show) {
      setShowMask(true);
      setSelectedIdx(maskInfo.selectedIndex);
    } else {
      setShowMask(false);
      setSelectedIdx(0);
    }
  }, [maskInfo]);
  
  return (
    <div className='flex flex-col px-2 self-stretch flex-1'>
      <div className='flex flex-col gap-2 self-stretch flex-1'>
        <div className='flex items-center gap-2'>
          <div className='flex flex-col self-stretch gap-1 w-full'>
            {/* <Button
              type='text'
            onClick={() => {
                const newShow = !showMask;
                setShowMask(newShow);
                onMaskChange({ masks: newShow ? masks[selectedIdx] : [], selectedIndex: selectedIdx, show: newShow });
              }}
            >
              <span className='font-source text-[12px] font-normal leading-[150%] text-AOI-blue'>
                {t('review.displayHideInvalidPart')}
              </span>
            </Button> */}
            {masks.length > 0 && (
              <Fragment>
              <span className='font-source text-[12px] font-normal leading-[150%] text-AOI-blue'>
                {t('review.displayHideInvalidPart')}
              </span>
              <Select
                popupMatchSelectWidth={false}
                style={{ width: '100%' }}
                size='small'
                value={showMask ? String(selectedIdx) : 'none'}
                onChange={(value) => {
                  triggerFromSelect.current = true;
                  if (value === 'none') {
                    setShowMask(false);
                    setSelectedIdx(0);
                    onMaskChange({ masks: [], selectedIndex: 0, show: false });
                  } else {
                    setShowMask(true);
                    setSelectedIdx(Number(value));
                    onMaskChange({ masks: masks[Number(value)], selectedIndex: Number(value), show: true });
                  }
                }}
                options={[{
                  label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('common.none')}
                    </span>, value: 'none'
                },
                  ...masks.map((m, i) => ({
                    label:
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('review.colorRange')}#{i + 1}
                      </span>,
                    value: String(i) }))
                ]}
              />
              </Fragment>
            )}
          </div>
        </div>
        <div className='flex flex-col gap-1 self-stretch h-[350px] overflow-auto'>
          <CommonTable
            cols={[
              {
                key: 'rangeIndex',
                title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('review.colorRange')}#</span>,
                render: (text, record) => {
                  return <span className='font-source text-[12px] font-normal leading-[150%]'>{record.index}</span>;
                },
                width: 100,
              },
              // {
              //   key: 'validRatio',
              //   title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('review.validRatio')}</span>,
              //   render: (text, record) => {
              //     return <span className='font-source text-[12px] font-normal leading-[150%]'>{_.round(record.value, 2)}</span>;
              //   },
              //   width: 70,
              // },
              {
                key: 'referenceRange',
                title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('review.referenceRange')}</span>,
                render: (text, record) => {
                  return <div className='flex flex-col gap-1 self-stretch'>
                    {/* <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {_.round(_.get(featureError, `parsedError.${validRatioList}.reference.list.${record.index}.param_range.ok_min`, 0), 2)}
                      {' ~ '}
                      {_.round(_.get(featureError, `parsedError.${validRatioList}.reference.list.${record.index}.param_range.ok_max`, 0), 2)}
                    </span> */}
                    <ParamDisplay
                      min={0}
                      max={100}
                      inferenceResult={record.value}
                      minThreshold={_.get(featureError, `parsedError.${validRatioList}.reference.list.${record.index}.param_range.ok_min`, 0)}
                      maxThreshold={_.get(featureError, `parsedError.${validRatioList}.reference.list.${record.index}.param_range.ok_max`, 0)}
                    />
                  </div>;
                },
              }
            ]}
            data={_.map(_.get(featureError, `parsedError.${validRatioList}.inferenceResult`, []), (v, i) => ({
              index: i,
              value: v,
            }))}
            total={_.size(_.get(featureError, `parsedError.${validRatioList}.inferenceResult`, []))}
            // isLoading={sessionsLoading}
          />
        </div>
      </div>
    </div>
  );
};

const Lead2DRatioList = (props) => {
  const {
    featureError,
    maskList,
    onMaskChange,
  } = props;
  // console.log('featureError', featureError);

  const { t } = useTranslation();

  const [activeTab, setActiveTab] = useState('solder');
  const [showMask, setShowMask] = useState(false);
  const masks = Array.isArray(_.get(maskList, activeTab)) ? _.get(maskList, activeTab) : (_.get(maskList, activeTab) ? [_.get(maskList, activeTab)] : []);

  return (
    <div className='flex flex-col px-2 self-stretch flex-1'>
      <div className='flex flex-col gap-2 self-stretch flex-1'>
        <div className='flex flex-col self-stretch gap-1'>
          <Button
            type='text'
            onClick={() => {
              const newShow = !showMask;
              setShowMask(newShow);
              onMaskChange({ masks: newShow ? masks : [], selectedIndex: 0, show: newShow });
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%] text-AOI-blue'>
              {t('review.displayHideInvalidPart')}
            </span>
          </Button>
          {/* no selection for lead v2 masks */}
          </div>
        <ConfigProvider
          theme={{
            components: {
              Tabs: {
                cardPadding: '4px 12px',
                horizontalMargin: '4px 4px 0 4px',
              },
            }
          }}
        >
          <Tabs
            type='card'
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key);
              if (showMask) {
                const newMasks = Array.isArray(_.get(maskList, key)) ? _.get(maskList, key) : (_.get(maskList, key) ? [_.get(maskList, key)] : []);
                onMaskChange({ masks: newMasks, selectedIndex: 0, show: true });
              }
            }}
            items={[
              {
                key: 'solder',
                label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.solder')}
                </span>,
              },
              {
                key: 'pad',
                label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.pad')}
                </span>,
              },
              {
                key: 'tip',
                label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.tip')}
                </span>,
              },
            ]}
          />
        </ConfigProvider>
        <div className='flex flex-col gap-1 self-stretch h-[350px] overflow-auto'>
        <CommonTable
          cols={[
            {
              key: 'leadIndex',
              title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('review.lead')}#</span>,
              render: (text, record) => {
                return <span className='font-source text-[12px] font-normal leading-[150%]'>{record.index}</span>;
              },
              width: 90,
            },
            // {
            //   key: 'validRatio',
            //   title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('review.validRatio')}</span>,
            //   render: (text, record) => {
            //     return <span className='font-source text-[12px] font-normal leading-[150%]'>{_.round(record.value, 2)}</span>;
            //   },
            //   width: 70,
            // },
            {
              key: 'referenceRange',
              title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>
                {activeTab === 'pad' ? t('review.validRatio') : t('review.referenceRange')}
              </span>,
              render: (text, record) => {
                if (activeTab === 'pad') {
                  return <span className='font-source text-[12px] font-normal leading-[150%]'>{_.round(record.value, 2)}</span>;
                }
                return (
                <div className='flex flex-col gap-1 self-stretch'>
                  {/* <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {_.round(_.get(featureError, `parsedError.${activeTab === 'solder' ? solderValidRatioList :
                      activeTab === 'tip' ? tipValidRatioList : ''}.reference.mapValidRangeMap.${
                        activeTab === 'solder' ? 'solder' : activeTab === 'tip' ? 'liftedLead': ''}.ok_min`, 0), 2)}
                    {' ~ '}
                    {_.round(_.get(featureError, `parsedError.${activeTab === 'solder' ? solderValidRatioList :
                      activeTab === 'tip' ? tipValidRatioList : ''}.reference.mapValidRangeMap.${
                        activeTab === 'solder' ? 'solder' : activeTab === 'tip' ? 'liftedLead': ''}.ok_max`, 0), 2)}
                    </span> */}
                    <ParamDisplay
                      min={0}
                      max={100}
                      inferenceResult={_.round(record.value, 2)}
                      minThreshold={_.round(_.get(featureError, `parsedError.${activeTab === 'solder' ? solderValidRatioList :
                        activeTab === 'tip' ? tipValidRatioList : ''}.reference.mapValidRangeMap.${
                          activeTab === 'solder' ? 'solder' : activeTab === 'tip' ? 'liftedLead': ''}.ok_min`, 0), 2)}
                      maxThreshold={_.round(_.get(featureError, `parsedError.${activeTab === 'solder' ? solderValidRatioList :
                        activeTab === 'tip' ? tipValidRatioList : ''}.reference.mapValidRangeMap.${
                          activeTab === 'solder' ? 'solder' : activeTab === 'tip' ? 'liftedLead': ''}.ok_max`, 0), 2)}
                    />
                  </div>
                );
              },
            },
          ]}
          data={_.map(_.get(featureError, `parsedError.${
            activeTab === 'solder' ? solderValidRatioList :
              activeTab === 'pad' ? padValidRatioList :
              activeTab === 'tip' ? tipValidRatioList : ''
          }.inferenceResult`, []), (v, i) => ({
            index: i,
            value: v,
          }))}
          total={_.size(_.get(featureError, `parsedError.${
            activeTab === 'solder' ? solderValidRatioList :
              activeTab === 'pad' ? padValidRatioList :
              activeTab === 'tip' ? tipValidRatioList : ''
          }.inferenceResult`, []))}
          // isLoading={sessionsLoading}
        />
        </div>
      </div>
    </div>
  );
};


const FilletVolumeRatio = (props) => {
  const {
    parsedError,
    filletOpenThreshold,
    filletUpperThreshold,
    filletLowerThreshold,
  } = props;

  const { t } = useTranslation();

  return (
    <div className='flex flex-col gap-1 self-stretch w-full justify-start items-start'>
      <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
        {t(`agentParamName.${solderInspection3D}.${filletVolumeRatio}`)}:
      </span>

      <ParamDisplay
        min={parsedError.reference.filletMinThreshold}
        max={parsedError.reference.filletMaxThreshold}
        inferenceResult={parsedError.inferenceResult}
        minThreshold={filletLowerThreshold}
        maxThreshold={filletUpperThreshold}
        type="range"
      />
    </div>
  );
};

const ParamMinMaxRange = (props) => {
  const {
    parsedError,
    errorType,
  } = props;

  const { t } = useTranslation();

  return (
    <div className='flex flex-col gap-1 self-stretch w-full justify-start items-start'>
      <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t(`agentParamName.${parsedError.detail}.${!_.includes(['height_max', 'height_min'], errorType) ? parsedError.referenceAgentParamName : errorType}`)}:
      </span>

      <ParamDisplay
        min={parsedError.reference.min}
        max={parsedError.reference.max}
        inferenceResult={parsedError.inferenceResult}
        minThreshold={parsedError.reference.okMin}
        maxThreshold={parsedError.reference.okMax}
        type="range"
      />
    </div>
  );
};

const ParamBool = (props) => {
  const {
    parsedError,
    errorType,
  } = props;

  const { t } = useTranslation();

  return (
    <div className='flex items-center gap-2 self-stretch'>
      <span className='font-source text-[12px] font-normal leading-[150%]'>
        {/* {t(`inferenceErrorType.${errorType}`)}: */}
        {t(`agentParamName.${parsedError.detail}.${parsedError.referenceAgentParamName}`)}:
      </span>
      <Tooltip
        title={
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('inferenceReferenceComparison.inferenceResult')}
          </span>
        }
      >
        <span className='font-source text-[12px] font-normal leading-[150%]'>
          {parsedError.inferenceResult ? 'true' : 'false'}
        </span>
      </Tooltip>
      {/* <Input
        style={{ width: '50%' }}
        value={parsedError.inferenceResult ? 'true' : 'false'}
      /> */}
      <Tooltip
        title={
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('inferenceReferenceComparison.referenceValue')}
          </span>
        }
      >
        <span className='font-source text-[12px] font-normal leading-[150%]'>
          {parsedError.reference.value ? 'true' : 'false'}
        </span>
      </Tooltip>
    </div>
  );
};

const ParamNumber = (props) => {
  const {
    parsedError,
    errorType,
  } = props;

  const { t } = useTranslation();
  const isGreenFromMinToThreshold = !parsedError.referenceAgentParamName.includes('min');

  return (

    <div className='flex flex-col gap-1 self-stretch w-full justify-start items-start'>
      <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap justify-start items-start'>
        {t(`agentParamName.${parsedError.detail}.${parsedError.referenceAgentParamName}`)}:
      </span>

      <ParamDisplay
        min={parsedError.reference.min}
        max={parsedError.reference.max}
        inferenceResult={parsedError.inferenceResult}
        threshold={parsedError.reference.value}
        type="single"
        isGreenFromMinToThreshold={isGreenFromMinToThreshold}
      />

    </div>
  );
};

const ParamString = (props) => {
  const {
    parsedError,
    errorType,
  } = props;

  const { t } = useTranslation();

  const { predicted, expected } = blurOcr(
    parsedError.inferenceResult,
    parsedError.reference.value,
    parsedError.wrongPositions || [],
  );

  const { predicted: groupPredicted, expected: groupExpected } = blurOcr(
    parsedError.inferenceResult,
    parsedError.reference.value,
    parsedError.groupEquivalence || [],
  );

  return (
    <div className='flex gap-4 self-stretch flex-col'>
      <div className='flex items-center gap-2 self-stretch flex-col'>
        <div className='flex items-center gap-2 self-stretch'>
          <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
            {t('common.textComparison')}
          </span>
          <Tooltip
            title={
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('review.ocrBlurTextDesc')}
              </span>
            }
          >
            <div className='flex items-center justify-center h-6 w-6'>
              <img
                src='/icn/info_white.svg'
                alt='info'
                className='w-3 h-3'
              />
            </div>
          </Tooltip>
        </div>
        <div className='flex items-center gap-2 self-stretch'>
          <div className='flex items-center gap-1'>
            <span className='font-source text-[12px] font-normal leading-[150%] text-[#fff]'>
              {t('common.inference')}
            </span>
            <div className='flex'>
              {predicted.map((p, idx) => (
                <span
                  key={`pred-${idx}`}
                  className={`font-source text-[12px] font-normal leading-[150%] ${p.highlight ? 'bg-red text-white' : ''}`}
                >
                  {p.char}
                </span>
              ))}
            </div>
          </div>
          <Button
            type='text'
            size='small'
            onClick={() => {
              navigator.clipboard.writeText(parsedError.inferenceResult);
            }}
          >
            <CopyOutlined />
          </Button>
        </div>
        <div className='flex items-center gap-2 self-stretch'>
          <div className='flex items-center gap-1'>
            <span className='font-source text-[12px] font-normal leading-[150%] text-[#fff]'>
              {t('common.reference')}
            </span>
            <div className='flex'>
              {expected.map((p, idx) => (
                <span
                  key={`exp-${idx}`}
                  className={`font-source text-[12px] font-normal leading-[150%] ${p.highlight ? 'bg-red text-white' : ''}`}
                >
                  {p.char}
                </span>
              ))}
            </div>
          </div>
          <Button
            size='small'
            type='text'
            onClick={() => {
              navigator.clipboard.writeText(parsedError.reference.value);
            }}
          >
            <CopyOutlined />
          </Button>
        </div>
      </div>

      {_.find(parsedError.groupEquivalence, (p) => p.highlight) && (
        <div className='flex flex-col gap-2 self-stretch'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
              {t('review.blurCharacter')}
            </span>
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            <div className='flex items-center gap-1'>
              <span className='font-source text-[12px] font-normal leading-[150%] text-[#fff]'>
                {t('common.inference')}
              </span>
              <div className='flex'>
                {groupPredicted.map((p, idx) => (
                  <span
                    key={`gp-${idx}`}
                    className={`font-source text-[12px] font-normal leading-[150%] ${p.highlight ? 'text-AOI-blue' : ''}`}
                  >
                    {p.char}
                  </span>
                ))}
              </div>
            </div>
            <Button
              type='text'
              size='small'
              onClick={() => {
                navigator.clipboard.writeText(parsedError.inferenceResult);
              }}
            >
              <CopyOutlined />
            </Button>
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            <div className='flex items-center gap-1'>
              <span className='font-source text-[12px] font-normal leading-[150%] text-[#fff]'>
                {t('common.reference')}
              </span>
              <div className='flex'>
                {groupExpected.map((p, idx) => (
                  <span
                    key={`ge-${idx}`}
                    className={`font-source text-[12px] font-normal leading-[150%] ${p.highlight ? 'text-AOI-blue' : ''}`}
                  >
                    {p.char}
                  </span>
                ))}
              </div>
            </div>
            <Button
              size='small'
              type='text'
              onClick={() => {
                navigator.clipboard.writeText(parsedError.reference.value);
              }}
            >
              <CopyOutlined />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

const ParamFloatList = (props) => {
  const { parsedError, errorType } = props;

  const { t } = useTranslation();

  return (
    <CustomCollapse
      style={{ width: '100%' }}
      items={[{
        key: errorType,
        label: (
          <div className='flex h-[32px] px-2 items-center gap-2'>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t(`agentParamName.${parsedError.detail}.${errorType}`)}
            </span>
          </div>
        ),
        children: (
          <div className='flex flex-col gap-1 p-2'>
            <table className='w-full text-left'>
              <thead>
                <tr>
                  <th className='font-source text-[12px] font-normal leading-[150%]'>#</th>
                  <th className='font-source text-[12px] font-normal leading-[150%]'>Value</th>
                </tr>
              </thead>
              <tbody>
                {_.map(parsedError.inferenceResult, (v, idx) => (
                  <tr key={idx}>
                    <td className='font-source text-[12px] font-normal leading-[150%]'>{idx + 1}</td>
                    <td className='font-source text-[12px] font-normal leading-[150%]'>{_.round(v, 4)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )
      }]}
    />
  );
};

export default InferenceResult;